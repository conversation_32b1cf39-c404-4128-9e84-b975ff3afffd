/**
 * invoice controller
 */

import {InvoiceManager} from "../services/invoice-manager";
import {capturePayment} from "../../payment-session/services/payment-capture";
import {InvoicePdf} from "../services/invoicePdf";
import fs from 'fs';
import path from 'path';

export default {


    /**
     * OCPI CDR-Endpoint GET (CPO → eMSP)
     * Liefert alle CDRs oder einen spezifischen CDR nach ID
     */
    async testendpunkt(ctx) {
        try {
            // Finde den ersten CDR
            const cdr = await strapi.documents('api::ocpi-cdr.ocpi-cdr').findFirst({
                populate: {
                    mandant: true,
                    payment_session: {
                        history: true,
                        populate: {
                        terminal: true
                        }
                    }
                }
            });

            if (!cdr) {
                return ctx.badRequest('No CDR found in database');
            }

            console.log('Found CDR:', cdr.documentId);


            const im = new InvoiceManager();
            const newInvoice = await im.createPreviewInvoice(cdr.payment_session.documentId);

            // Hole die Payter-Connection für das Terminal
            const terminalId = cdr?.payment_session.terminal?.serialNumber;
            if (!terminalId) {
                throw new Error(`Kein Terminal für Payment-Session ${cdr?.payment_session.documentId} gefunden`);
            }
            ctx.state.terminal = cdr?.payment_session.terminal;
            // Hole den API-Client für die Payter-API
            const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

            if (!apiClient) {
                throw new Error('Konnte keinen API-Client für die Payter-API erstellen');
            }
            let amountToCapture = newInvoice.sum_gross * 100;

            if(newInvoice.sum_gross * 100 > cdr.payment_session.blockedAmount) {
                amountToCapture = cdr.payment_session.blockedAmount
            }
            // Führe die Capture-Operation durch
            const captureResult = await capturePayment(
                apiClient,
                terminalId,
                cdr?.payment_session.paymentIntent,
                amountToCapture
            );

            // Überprüfe das Ergebnis der Capture-Operation
            if (!captureResult.success) {
                console.error(`Fehler beim Belasten der Kreditkarte: ${captureResult.message}`);


                const updatedHistory = await strapi.service('api::payment-session.payment-session').formatHistory(
                    cdr?.payment_session.documentId,
                    'capture_failed',
                    amountToCapture,
                    captureResult
                );

                // Aktualisiere die Payment-Session mit dem Fehlerstatus
                const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
                    documentId: cdr?.payment_session.documentId,
                    data: {
                        paymentSessionState: 'error',
                        history:updatedHistory
                    }
                });
                return {
                    success: false,
                    paymentSession: updatedPaymentSession,
                    error: captureResult.error,
                    message: captureResult.message
                };
            }
            // update payment_session and set amount to capture auf den newInvoice amount gross
            if (newInvoice && cdr.payment_session) {
                // Berechne den zu belastenden Betrag in Cent (Bruttobetrag)

                const updatedHistory = await strapi.service('api::payment-session.payment-session').formatHistory(
                    cdr?.payment_session.documentId,
                    {
                        action: 'capture',
                        amount: amountToCapture,
                        timestamp: new Date().toISOString(),
                        result: captureResult
                    }
                );

                // Aktualisiere die Payment-Session mit dem erfolgreichen Status
                const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
                    documentId: cdr.payment_session.documentId,
                    data: { paymentSessionState: 'captured',
                        capturedAmount: amountToCapture,
                        closedAt: new Date(),
                        history: updatedHistory}

                });

                console.log(`Payment session ${cdr.payment_session.documentId} updated with amount to capture: ${amountToCapture / 100} €`);
            }

            // Finalisiere die Rechnung
            const finalizedInvoice = await im.finalize(newInvoice?.documentId);
            const paidInvoice = await im.setStatusToPaid(finalizedInvoice.documentId);
            return ctx.send({
                message: 'Invoice created and finalized',
                invoice: paidInvoice
            });

        } catch (error) {
            console.error('Error in testendpunkt:', error);
            return ctx.badRequest(`Error creating invoice: ${error.message}`);
        }
    },

    /**
     * Generiert manuell eine PDF für eine bestehende Invoice
     * POST /api/invoices/:id/generate-pdf
     */
    async generatePdf(ctx) {
        try {
            const { id } = ctx.params;

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Hole die Invoice mit allen benötigten Relationen
            const invoice = await strapi.documents('api::invoice.invoice').findOne({
                documentId: id,
                populate: {
                    mandant: {
                        populate: ['logo']
                    },
                    invoice_positions: true,
                    ocpi_cdr: true,
                    ocpi_session: true,
                    payment_session: true,
                    file: true
                }
            });

            if (!invoice) {
                return ctx.notFound('Invoice not found');
            }

            // Prüfe, ob die Invoice bereits eine Rechnungsnummer hat
            if (!invoice.invoice_number) {
                return ctx.badRequest('Invoice must have an invoice number before PDF generation. Please finalize the invoice first.');
            }

            // Prüfe, ob bereits eine PDF-Datei existiert
            const existingPdfFile = invoice.file?.find(file =>
                file.name?.includes('Invoice_') && file.ext === '.pdf'
            );

            if (existingPdfFile) {
                console.log(`PDF already exists for invoice ${invoice.invoice_number}: ${existingPdfFile.name}`);

                // Optional: Überschreiben oder Fehler zurückgeben
                const { force } = ctx.request.query;
                if (!force) {
                    return ctx.badRequest({
                        message: 'PDF already exists for this invoice',
                        existingFile: existingPdfFile,
                        hint: 'Use ?force=true to regenerate the PDF'
                    });
                }
            }

            console.log(`Generating PDF for invoice ${invoice.invoice_number}...`);

            // Erstelle die PDF
            const invoicePdf = new InvoicePdf(invoice);
            await invoicePdf.init();

            if (!invoicePdf.filepath) {
                console.error('PDF generation failed: No filepath returned');
                return ctx.internalServerError('PDF generation failed');
            }

            // Prüfe, ob die PDF-Datei tatsächlich erstellt wurde
            if (!fs.existsSync(invoicePdf.filepath)) {
                console.error(`PDF file not found at path: ${invoicePdf.filepath}`);
                return ctx.internalServerError('PDF file was not created');
            }

            console.log(`PDF successfully created at: ${invoicePdf.filepath}`);

            // Aktualisiere die Invoice mit dem Status
            const updatedInvoice = await strapi.documents('api::invoice.invoice').update({
                documentId: id,
                data: {
                    invoice_status: 'INMUTABLE_WRITTEN' // Setze Status auf unveränderlich
                },
                populate: {
                    mandant: true,
                    invoice_positions: true
                }
            });

            // Erstelle Datei-Informationen für die Antwort
            const fileName = path.basename(invoicePdf.filepath);
            const fileStats = fs.statSync(invoicePdf.filepath);

            return ctx.send({
                message: 'PDF generated successfully',
                invoice: {
                    documentId: updatedInvoice.documentId,
                    invoice_number: updatedInvoice.invoice_number,
                    invoice_status: updatedInvoice.invoice_status,
                    sum_gross: updatedInvoice.sum_gross,
                    sum_net: updatedInvoice.sum_net,
                    vat_amount: updatedInvoice.vat_amount
                },
                pdf: {
                    fileName: fileName,
                    filePath: invoicePdf.filepath,
                    fileSize: fileStats.size,
                    created: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error generating PDF:', error);
            return ctx.internalServerError({
                message: 'Failed to generate PDF',
                error: error.message
            });
        }
    },

    /**
     * Lädt eine bestehende PDF-Datei für eine Invoice herunter
     * GET /api/invoices/:id/download-pdf
     */
    async downloadPdf(ctx) {
        try {
            const { id } = ctx.params;

            if (!id) {
                return ctx.badRequest('Invoice ID is required');
            }

            // Hole die Invoice
            const invoice = await strapi.documents('api::invoice.invoice').findOne({
                documentId: id,
                populate: {
                    mandant: true
                }
            });

            if (!invoice) {
                return ctx.notFound('Invoice not found');
            }

            if (!invoice.invoice_number) {
                return ctx.badRequest('Invoice has no invoice number');
            }

            // Konstruiere den PDF-Pfad basierend auf der bestehenden Logik
            const date = new Date();
            const year = date.getFullYear();
            const month = date.getMonth() + 1;

            const invoiceFolder = strapi.config.get('server.invoiceFolder', '/tmp/invoices');
            const fileName = `Invoice_${invoice.invoice_number}.pdf`;
            const pdfPath = path.join(invoiceFolder, String(year), String(month), fileName);

            // Prüfe, ob die PDF-Datei existiert
            if (!fs.existsSync(pdfPath)) {
                return ctx.notFound({
                    message: 'PDF file not found',
                    hint: 'Use POST /invoices/:id/generate-pdf to create the PDF first',
                    expectedPath: pdfPath
                });
            }

            // Setze die entsprechenden Headers für PDF-Download
            ctx.set('Content-Type', 'application/pdf');
            ctx.set('Content-Disposition', `attachment; filename="${fileName}"`);

            // Lese und sende die PDF-Datei
            const fileBuffer = fs.readFileSync(pdfPath);
            ctx.body = fileBuffer;

        } catch (error) {
            console.error('Error downloading PDF:', error);
            return ctx.internalServerError({
                message: 'Failed to download PDF',
                error: error.message
            });
        }
    },

    /**
     * Findet alle Invoices, für die keine PDF-Datei existiert
     * GET /api/invoices/missing-pdfs
     */
    async findMissingPdfs(ctx) {
        try {
            // Hole alle finalisierten Invoices mit Rechnungsnummer
            const invoices = await strapi.documents('api::invoice.invoice').findMany({
                filters: {
                    invoice_number: {
                        $notNull: true
                    },
                    invoice_status: {
                        $in: ['INMUTABLE_WRITTEN', 'PAID']
                    }
                },
                populate: {
                    mandant: true,
                    payment_session: true
                },
                sort: { createdAt: 'desc' }
            });

            const missingPdfs = [];
            const invoiceFolder = strapi.config.get('server.invoiceFolder', '/tmp/invoices');

            for (const invoice of invoices) {
                if (!invoice.invoice_number) continue;

                // Konstruiere den erwarteten PDF-Pfad
                const date = new Date(invoice.createdAt);
                const year = date.getFullYear();
                const month = date.getMonth() + 1;

                const fileName = `Invoice_${invoice.invoice_number}.pdf`;
                const pdfPath = path.join(invoiceFolder, String(year), String(month), fileName);

                // Prüfe, ob die PDF-Datei existiert
                if (!fs.existsSync(pdfPath)) {
                    missingPdfs.push({
                        documentId: invoice.documentId,
                        invoice_number: invoice.invoice_number,
                        invoice_status: invoice.invoice_status,
                        createdAt: invoice.createdAt,
                        mandant: invoice.mandant?.name,
                        expectedPdfPath: pdfPath,
                        sum_gross: invoice.sum_gross,
                        payment_session_id: invoice.payment_session?.documentId
                    });
                }
            }

            return ctx.send({
                message: `Found ${missingPdfs.length} invoices without PDF files`,
                count: missingPdfs.length,
                total_invoices: invoices.length,
                missing_pdfs: missingPdfs
            });

        } catch (error) {
            console.error('Error finding missing PDFs:', error);
            return ctx.internalServerError({
                message: 'Failed to find missing PDFs',
                error: error.message
            });
        }
    }
}

