

export default {
    routes: [
        {
            method: 'POST',
            path: '/invoices/testendpunk',
            handler: 'invoice.testendpunkt',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'POST',
            path: '/invoices/:id/generate-pdf',
            handler: 'invoice.generatePdf',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'GET',
            path: '/invoices/:id/download-pdf',
            handler: 'invoice.downloadPdf',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'GET',
            path: '/invoices/missing-pdfs',
            handler: 'invoice.findMissingPdfs',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },

        // Weitere benutzerdefinierte Routen hier...
    ]
};